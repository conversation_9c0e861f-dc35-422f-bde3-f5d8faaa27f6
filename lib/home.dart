import 'dart:math' as math;

import 'package:flutter/material.dart';

import 'config/constants.dart';
import 'content/content_home_page.dart';
import 'home/widgets/background_painter.dart';
import 'home/widgets/home_tool_card.dart';
import 'html/html_manager_screen.dart';
import 'markdown/markdown_render_screen.dart';
import 'settings/settings_screen.dart';
import 'svg/svg_manager_screen.dart';
import 'text_cards/text_cards_home_page.dart';
import 'traffic_guide/traffic_guide_home_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      extendBodyBehindAppBar: true, // 允许内容延伸到应用栏后面
      body: Stack(
        children: [
          // 背景渐变动画
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: BackgroundPainter(_animationController.value),
                );
              },
            ),
          ),
          // 主内容
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 应用栏
              SliverAppBar(
                expandedHeight: 130,
                floating: false,
                pinned: true,
                stretch: true,
                elevation: _isScrolled ? 4 : 0,
                backgroundColor:
                    _isScrolled
                        ? colorScheme.surface.withValues(alpha: 0.95)
                        : Colors.transparent,
                title:
                    _isScrolled
                        ? Text(
                          MediaQuery.of(context).size.width > 320
                              ? AppConstants.appNameChinese
                              : AppConstants.appName,
                          style: TextStyle(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        )
                        : null,
                flexibleSpace: FlexibleSpaceBar(
                  title: null,
                  centerTitle: false,
                  collapseMode: CollapseMode.pin,
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // 顶部渐变背景
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              colorScheme.primary.withValues(alpha: 0.7),
                              colorScheme.primary.withValues(alpha: 0.5),
                              colorScheme.primary.withValues(alpha: 0.2),
                              Colors.transparent,
                            ],
                          ),
                        ),
                      ),
                      // 顶部装饰图案
                      Positioned(
                        top: -30,
                        right: -30,
                        child: Transform.rotate(
                          angle: math.pi / 6,
                          child: Container(
                            width: 180,
                            height: 180,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  colorScheme.primary,
                                  colorScheme.secondary,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(40),
                            ),
                          ),
                        ),
                      ),
                      // 应用标题区
                      Positioned(
                        left: 20,
                        bottom: 35,
                        child: Text(
                          MediaQuery.of(context).size.width > 320
                              ? AppConstants.appNameChinese
                              : AppConstants.appName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 28,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                actions: [
                  IconButton(
                    icon: Icon(
                      Icons.settings,
                      color:
                          _isScrolled
                              ? colorScheme.onSurface : Colors.white,
                    ),
                    onPressed: () {
                      // 打开设置页面
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SettingsScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),

              // 内容管理区
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ContentHomePage(),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      height: 90,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            colorScheme.primaryContainer,
                            colorScheme.secondaryContainer,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: colorScheme.primary.withValues(alpha: 0.3),
                            offset: const Offset(0, 4),
                            blurRadius: 15,
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 50,
                            height: 50,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.25),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(
                              Icons.dashboard_rounded,
                              color: Colors.white,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                  '我的内容库',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 20,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  '管理和浏览您的所有内容',
                                  style: TextStyle(
                                    color: Colors.white.withValues(alpha: 0.85),
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Icon(Icons.arrow_forward, color: Colors.white),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // 内容工具标题
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 26, 20, 16),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.apps_rounded,
                          size: 16,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        '推荐工具',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 工具网格
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 20),
                sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.0,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  delegate: SliverChildListDelegate([
                    HomeToolCard(
                      title: 'Markdown',
                      description: '文档编辑与渲染',
                      icon: Icons.text_fields,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.primary,
                          colorScheme.primary.withValues(alpha: 0.7),
                        ],
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const MarkdownRenderScreen(),
                          ),
                        );
                      },
                    ),
                    HomeToolCard(
                      title: '文本卡片',
                      description: '知识卡片定制渲染',
                      icon: Icons.text_snippet,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.secondary,
                          colorScheme.secondary.withValues(alpha: 0.7),
                        ],
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TextCardsHomePage(),
                          ),
                        );
                      },
                    ),
                    HomeToolCard(
                      title: '内容引流',
                      description: '引流图片与文本处理',
                      icon: Icons.trending_up,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.tertiary,
                          colorScheme.tertiary.withValues(alpha: 0.7),
                        ],
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const TrafficGuideHomePage(),
                          ),
                        );
                      },
                    ),
                    // HomeToolCard(
                    //   title: '语音',
                    //   description: '录制与文本转换',
                    //   icon: Icons.mic,
                    //   gradient: AppTheme.purpleGradient,
                    //   onTap: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(
                    //         builder: (context) => const VoiceHomePage(),
                    //       ),
                    //     );
                    //   },
                    // ),
                  ]),
                ),
              ),

              // 内容工具标题
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 26, 20, 16),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Icon(
                          Icons.apps_rounded,
                          size: 16,
                          color: colorScheme.primary,
                        ),
                      ),
                      const SizedBox(width: 10),
                      Text(
                        '文件工具',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 工具网格
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 20),
                sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 2.0,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  delegate: SliverChildListDelegate([
                    HomeToolCard(
                      title: 'SVG',
                      description: '矢量图形处理',
                      icon: Icons.image,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.primaryContainer,
                          colorScheme.primaryContainer.withValues(alpha: 0.7),
                        ],
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SvgManagerScreen(),
                          ),
                        );
                      },
                    ),
                    HomeToolCard(
                      title: 'HTML',
                      description: '网页内容编辑',
                      icon: Icons.code,
                      gradient: LinearGradient(
                        colors: [
                          colorScheme.secondaryContainer,
                          colorScheme.secondaryContainer.withValues(alpha: 0.7),
                        ],
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const HtmlManagerScreen(),
                          ),
                        );
                      },
                    ),
                    // HomeToolCard(
                    //   title: 'PDF',
                    //   description: '文档查看与注释',
                    //   icon: Icons.picture_as_pdf,
                    //   gradient: AppTheme.orangeGradient,
                    //   onTap: () {
                    //     Navigator.push(
                    //       context,
                    //       MaterialPageRoute(
                    //         builder: (context) => const PdfManagerScreen(),
                    //       ),
                    //     );
                    //   },
                    // ),
                  ]),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
